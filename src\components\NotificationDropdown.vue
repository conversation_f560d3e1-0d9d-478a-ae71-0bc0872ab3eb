<template>
  <div class="notification-dropdown position-relative">
    <!-- Bo<PERSON><PERSON> trigger -->
    <div
      class="notification-trigger d-flex align-items-center justify-content-center position-relative"
      @click="toggleDropdown"
      :class="{ 'active': isOpen }"
    >
      <!-- Setinha indicadora -->
      <div class="dropdown-arrow me-2">
        <v-icon
          size="16"
          color="white"
          :class="{ 'rotated': isOpen }"
        >
          mdi-chevron-down
        </v-icon>
      </div>

      <!-- Foto do usuário ou sino -->
      <div v-if="!hasUnreadNotifications" class="user-avatar">
        <img
          :src="userProfilePicture"
          :alt="$user?.nome || 'Usuário'"
          class="rounded-circle profile-image"
          @error="onImageError"
        />
      </div>

      <!-- Sino com badge -->
      <div v-else class="notification-bell position-relative">
        <v-icon size="24" color="white">mdi-bell</v-icon>
        <span
          v-if="unreadCount > 0"
          class="notification-badge position-absolute"
        >
          {{ unreadCount > 99 ? '99+' : unreadCount }}
        </span>
      </div>
    </div>

    <!-- Dropdown -->
    <div
      v-if="isOpen"
      class="notification-dropdown-menu position-absolute dropdown-animate"
      :class="{ 'closing': isClosing }"
      @click.stop
    >
      <!-- Nome do usuário -->
      <div class="user-info-header">
        <div class="user-name-header">
          {{ userName }}
        </div>
      </div>

      <div class="dropdown-header d-flex justify-content-between align-items-center">
        <div class="d-flex align-items-center">
          <h6 class="mb-0 me-2">Notificações</h6>
          <!-- Spinner discreto para refresh -->
          <div v-if="isRefreshing" class="spinner-border spinner-border-sm text-primary" role="status" style="width: 12px; height: 12px;">
            <span class="visually-hidden">Atualizando...</span>
          </div>
        </div>
        <div class="dropdown-actions">
          <button
            v-if="hasUnreadNotifications"
            @click="markAllAsRead"
            class="btn btn-sm btn-link p-0 text-primary"
            :disabled="isLoading || isRefreshing"
          >
            Marcar todas como lidas
          </button>
        </div>
      </div>

      <div class="dropdown-body">
        <!-- Loading -->
        <div v-if="isLoading" class="text-center py-3">
          <div class="spinner-border spinner-border-sm" role="status">
            <span class="visually-hidden">Carregando...</span>
          </div>
        </div>

        <!-- Erro -->
        <div v-else-if="error" class="alert alert-danger alert-sm mb-0">
          {{ error }}
          <button @click="retryFetch" class="btn btn-sm btn-link p-0 ms-2">
            Tentar novamente
          </button>
        </div>

        <!-- Sem notificações -->
        <div v-else-if="!hasNotifications" class="empty-state text-center py-4">
          <v-icon size="48" color="grey-lighten-1" class="mb-2">mdi-bell-outline</v-icon>
          <p class="text-muted mb-0">Sem notificações</p>
        </div>

        <!-- Lista de notificações -->
        <div v-else class="notifications-list">
          <div 
            v-for="notification in displayedNotifications" 
            :key="notification.id"
            class="notification-item"
            :class="{ 'unread': !notification.read }"
            @click="handleNotificationClick(notification)"
          >
            <div class="notification-icon">
              <v-icon 
                :color="getNotificationColor(notification.type)"
                size="20"
              >
                {{ getNotificationIcon(notification.type) }}
              </v-icon>
            </div>
            
            <div class="notification-content flex-grow-1">
              <div class="notification-title">{{ notification.title }}</div>
              <div class="notification-message">{{ notification.message }}</div>
              <div class="notification-time">{{ formatDate(notification.created_at) }}</div>
            </div>
            
            <div class="notification-actions">
              <button 
                @click.stop="toggleReadStatus(notification)"
                class="btn btn-sm btn-link p-1"
                :title="notification.read ? 'Marcar como não lida' : 'Marcar como lida'"
              >
                <v-icon size="16" :color="notification.read ? 'grey' : 'primary'">
                  {{ notification.read ? 'mdi-email-open' : 'mdi-email' }}
                </v-icon>
              </button>
              
              <button 
                @click.stop="deleteNotification(notification.id)"
                class="btn btn-sm btn-link p-1 text-danger"
                title="Excluir notificação"
              >
                <v-icon size="16">mdi-delete</v-icon>
              </button>
            </div>
          </div>
        </div>
      </div>

      <div class="dropdown-footer">
        <div class="d-flex justify-content-between align-items-center mb-3">
          <button
            @click="showAllNotifications"
            class="btn btn-sm btn-link p-0"
          >
            Ver todas notificações
          </button>
        </div>

        <!-- Botão de Configurações ocupando largura total -->
        <div class="mb-3">
          <button
            @click="showSettings"
            class="btn btn-outline-primary w-100 d-flex align-items-center justify-content-center"
          >
            <v-icon size="18" class="me-2">mdi-cog</v-icon>
            Configurações
          </button>
        </div>

        <!-- Botões Suporte e Sair lado a lado -->
        <div class="d-flex gap-2">
          <button
            @click="showSupport"
            class="btn btn-outline-secondary flex-fill d-flex align-items-center justify-content-center"
          >
            <v-icon size="16" class="me-2" style="margin-top: -1px;">mdi-headset</v-icon>
            Suporte
          </button>
          <button
            @click="confirmLogout"
            class="btn btn-outline-danger flex-fill d-flex align-items-center justify-content-center"
          >
            <v-icon size="16" class="me-2" style="margin-top: -1px;">mdi-logout</v-icon>
            Sair
          </button>
        </div>
      </div>
    </div>

    <!-- Overlay para fechar dropdown -->
    <div
      v-if="isOpen"
      class="dropdown-overlay position-fixed"
      @click="closeDropdown"
    ></div>

    <!-- Modal de todas as notificações -->
    <notifications-modal ref="notificationsModal" />
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'
import notificationsService from '@/services/notificationsService'
import usuariosService from '@/services/usuariosService'
import NotificationsModal from './NotificationsModal.vue'
import { openModal } from "@/utils/modalHelper.js";
import cSwal from "@/utils/cSwal.js";

export default {
  name: 'NotificationDropdown',

  components: {
    NotificationsModal
  },
  
  data() {
    return {
      isOpen: false,
      isClosing: false,
      defaultAvatar: '/favicon.png',
      imageErrorOccurred: false
    }
  },
  
  computed: {
    ...mapGetters('notifications', [
      'allNotifications',
      'unreadNotifications',
      'unreadCount',
      'hasNotifications',
      'hasUnreadNotifications',
      'isLoading',
      'isRefreshing',
      'error'
    ]),
    
    displayedNotifications() {
      // Mostrar apenas as 10 mais recentes no dropdown
      return this.allNotifications.slice(0, 10)
    },
    
    userProfilePicture() {
      // Tentar obter foto do perfil do usuário
      const user = this.$user
      if (user?.dentista?.profile_picture_url) {
        return user.dentista.profile_picture_url
      }
      return this.defaultAvatar
    },

    userName() {
      // Obter nome do usuário logado
      const user = this.$user
      if (user?.nome) {
        return user.nome
      }
      if (user?.dentista?.nome) {
        return user.dentista.nome
      }
      return 'Usuário'
    }
  },
  
  async mounted() {
    // Verificar se já foi inicializado para evitar múltiplas inicializações
    if (!notificationsService.isInitialized) {
      // Inicializar serviço de notificações
      await notificationsService.init()
    }

    // Buscar notificações iniciais apenas se não há dados
    if (!this.hasNotifications) {
      this.fetchInitialNotifications()
    }

    // Escutar cliques fora do componente
    document.addEventListener('click', this.handleOutsideClick)
  },
  
  beforeUnmount() {
    document.removeEventListener('click', this.handleOutsideClick)
    notificationsService.destroy()
  },

  watch: {
    // Observar mudanças na imagem do usuário para resetar flag de erro
    userProfilePicture(newValue, oldValue) {
      if (newValue !== oldValue && newValue !== this.defaultAvatar) {
        this.imageErrorOccurred = false
      }
    }
  },

  methods: {
    ...mapActions('notifications', [
      'fetchNotifications',
      'markAsRead',
      'markAsUnread', 
      'markAllAsRead',
      'deleteNotification'
    ]),
    
    async fetchInitialNotifications() {
      try {
        await this.fetchNotifications({ perPage: 10 })
      } catch (error) {
        console.error('Erro ao carregar notificações iniciais:', error)
      }
    },

    async refreshNotifications() {
      try {
        await notificationsService.refreshNotifications({ perPage: 10 })
      } catch (error) {
        console.error('Erro ao atualizar notificações:', error)
      }
    },
    
    toggleDropdown() {
      if (this.isOpen) {
        this.closeDropdown()
      } else {
        this.openDropdown()
      }
    },

    openDropdown() {
      this.isOpen = true
      this.isClosing = false

      if (this.hasNotifications) {
        // Se já há notificações, fazer refresh sem limpar a lista
        this.refreshNotifications()
      } else {
        // Se não há notificações, fazer fetch inicial
        this.fetchInitialNotifications()
      }
    },

    closeDropdown() {
      if (!this.isOpen) return

      this.isClosing = true

      // Aguardar a animação de saída antes de fechar
      setTimeout(() => {
        this.isOpen = false
        this.isClosing = false
      }, 150) // Duração da animação de saída
    },
    
    handleOutsideClick(event) {
      if (!this.$el.contains(event.target)) {
        this.closeDropdown()
      }
    },
    
    async handleNotificationClick(notification) {
      // Marcar como lida se não estiver
      if (!notification.read) {
        await this.markAsRead(notification.id)
      }
      
      // Navegar para URL de ação se existir
      if (notification.action_url) {
        this.$router.push(notification.action_url)
        this.closeDropdown()
      }
    },
    
    async toggleReadStatus(notification) {
      try {
        if (notification.read) {
          await this.markAsUnread(notification.id)
        } else {
          await this.markAsRead(notification.id)
        }
      } catch (error) {
        console.error('Erro ao alterar status de leitura:', error)
      }
    },
    
    async markAllAsRead() {
      try {
        await notificationsService.markAllAsRead()
      } catch (error) {
        console.error('Erro ao marcar todas como lidas:', error)
      }
    },
    
    async deleteNotification(notificationId) {
      if (confirm('Tem certeza que deseja excluir esta notificação?')) {
        try {
          await this['deleteNotification'](notificationId)
        } catch (error) {
          console.error('Erro ao excluir notificação:', error)
        }
      }
    },
    
    showAllNotifications() {
      // Abrir modal de todas as notificações
      const modalElement = document.getElementById('notificationsModal')
      if (modalElement) {
        openModal('notificationsModal')
      }
      this.closeDropdown()
    },
    
    showSettings() {
      // Navegar para página de configurações
      this.$router.push('/configuracoes')
      this.closeDropdown()
    },

    showSupport() {
      // Implementar página de suporte
      this.$router.push('/suporte')
      this.closeDropdown()
    },

    confirmLogout() {
      // Confirmação de logout usando cSwal
      cSwal.cConfirm('Deseja realmente sair do sistema?', () => {
        // Lógica de logout
        cSwal.loading('Saindo...');
        window.setTimeout(() => {
          usuariosService.logout(() => {
            cSwal.loaded();
            this.closeDropdown()
          });
        }, 200)
      });
    },
    
    retryFetch() {
      this.fetchInitialNotifications()
    },
    
    onImageError(event) {
      // Evitar loop infinito - só trocar a imagem uma vez
      if (!this.imageErrorOccurred) {
        this.imageErrorOccurred = true
        event.target.src = this.defaultAvatar
      }
    },
    
    formatDate(dateString) {
      return notificationsService.formatNotificationDate(dateString)
    },
    
    getNotificationIcon(type) {
      return notificationsService.getNotificationIcon(type)
    },
    
    getNotificationColor(type) {
      return notificationsService.getNotificationColor(type)
    }
  }
}
</script>

<style scoped>
.notification-dropdown {
  z-index: 1050;
}

.notification-trigger {
  /* width: 70px; */
  padding-right: 15px;
  padding-left: 8px;
  height: 48px;
  cursor: pointer;
  border-radius: 3px 0px 0px 3px;
  transition: all 0.2s ease;
  background-color: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.notification-trigger:hover {
  background-color: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.1);
}

.notification-trigger.active {
  background-color: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.2);
}

.dropdown-arrow {
  transition: transform 0.25s cubic-bezier(0.4, 0, 0.2, 1);
}

.dropdown-arrow .v-icon {
  transition: transform 0.25s cubic-bezier(0.4, 0, 0.2, 1);
}

.dropdown-arrow .rotated {
  transform: rotate(180deg);
}

.user-avatar {
  position: relative;
}

.user-avatar .profile-image {
  width: 38px;
  height: 38px;
  object-fit: cover;
  border: 2px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.notification-trigger:hover .profile-image {
  border-color: rgba(255, 255, 255, 0.5);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
}

.notification-bell {
  width: 38px;
  padding-right: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.notification-badge {
  top: -2px;
  right: 4px;
  background-color: #dc3545;
  color: white;
  border-radius: 10px;
  padding: 2px 6px;
  font-size: 10px;
  font-weight: bold;
  min-width: 18px;
  text-align: center;
  line-height: 1.2;
}

.notification-dropdown-menu {
  top: 100%;
  right: 0;
  width: 380px;
  max-height: 700px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  border: 1px solid #e0e0e0;
  overflow: hidden;
  z-index: 1051;
}

/* Animação elegante do dropdown */
.dropdown-animate {
  animation: dropdownSlideIn 0.2s ease-out;
  transform-origin: top right;
}

.dropdown-animate.closing {
  animation: dropdownSlideOut 0.15s ease-in;
}

@keyframes dropdownSlideIn {
  0% {
    opacity: 0;
    transform: translateY(-15px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes dropdownSlideOut {
  0% {
    opacity: 1;
    transform: translateY(0);
  }
  100% {
    opacity: 0;
    transform: translateY(15px);
  }
}

/* Header do nome do usuário */
.user-info-header {
  padding: 14px 20px 10px 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.user-name-header {
  text-align: center;
  font-weight: 400;
  font-size: 13px;
  color: #6c757d;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  opacity: 0.8;
}

.dropdown-header {
  padding: 12px 20px;
  border-bottom: 1px solid #f0f0f0;
  background-color: #fafafa;
}

.dropdown-header h6 {
  font-weight: 600;
  color: #333;
}

.dropdown-body {
  max-height: 350px;
  overflow-y: auto;
}

.empty-state {
  padding: 40px 20px;
}

.notifications-list {
  padding: 0;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  padding: 12px 20px;
  border-bottom: 1px solid #f5f5f5;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.notification-item:hover {
  background-color: #f8f9fa;
}

.notification-item.unread {
  background-color: #f0f8ff;
  border-left: 3px solid #007bff;
}

.notification-item:last-child {
  border-bottom: none;
}

.notification-icon {
  margin-right: 12px;
  margin-top: 2px;
}

.notification-content {
  min-width: 0;
}

.notification-title {
  font-weight: 600;
  font-size: 14px;
  color: #333;
  margin-bottom: 4px;
  line-height: 1.3;
}

.notification-message {
  font-size: 13px;
  color: #666;
  margin-bottom: 4px;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.notification-time {
  font-size: 11px;
  color: #999;
}

.notification-actions {
  display: flex;
  align-items: flex-start;
  margin-left: 8px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.notification-item:hover .notification-actions {
  opacity: 1;
}

.dropdown-footer {
  padding: 16px 20px;
  border-top: 1px solid #f0f0f0;
  background-color: #fafafa;
}



.dropdown-footer .btn {
  font-size: 13px;
  font-weight: 500;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.dropdown-footer .btn-outline-primary {
  border-color: #007bff;
  color: #007bff;
}

.dropdown-footer .btn-outline-primary:hover {
  background-color: #007bff;
  color: white;
}

.dropdown-footer .btn-outline-secondary {
  border-color: #6c757d;
  color: #6c757d;
}

.dropdown-footer .btn-outline-secondary:hover {
  background-color: #6c757d;
  color: white;
}

.dropdown-footer .btn-outline-danger {
  border-color: #dc3545;
  color: #dc3545;
}

.dropdown-footer .btn-outline-danger:hover {
  background-color: #dc3545;
  color: white;
}

.dropdown-overlay {
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1049;
}

/* Scrollbar customizada */
.dropdown-body::-webkit-scrollbar {
  width: 4px;
}

.dropdown-body::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.dropdown-body::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.dropdown-body::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Responsivo */
@media (max-width: 768px) {
  .notification-dropdown-menu {
    width: 320px;
    right: -20px;
  }
}

@media (max-width: 480px) {
  .notification-dropdown-menu {
    width: 280px;
    right: -40px;
  }

  .notification-item {
    padding: 10px 16px;
  }

  .dropdown-header,
  .dropdown-footer {
    padding: 12px 16px;
  }
}
</style>
