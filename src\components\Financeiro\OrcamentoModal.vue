<template>
  <div class="modal fade" id="modalOrcamento" tabindex="-1" aria-labelledby="modalOrcamentoLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-centered">
      <div class="modal-content elegant-modal">
        <div class="modal-header elegant-header">
          <div class="d-flex align-items-center">
            <div class="modal-icon">
              <i class="fas fa-calculator"></i>
            </div>
            <div>
              <h5 class="modal-title mb-0" id="modalOrcamentoLabel">Novo Orçamento</h5>
              <small class="text-muted">Criar um novo orçamento para o paciente</small>
            </div>
          </div>
          <button type="button" class="btn-close elegant-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        
        <div class="modal-body elegant-body">
          <orcamento-form
            :form="orcamentoForm"
            :errors="errors"
            :pacientes="pacientes"
            :dentistas="dentistas"
            :servicos-produtos="servicosProdutos"
            :preselected-paciente="preselectedPaciente"
            :paciente-selecionado="pacienteSelecionado"
            @update-form="updateOrcamentoForm"
            @add-item="addOrcamentoItem"
            @remove-item="removeOrcamentoItem"
            @search-servicos="searchServicos"
            @abrir-busca-paciente="abrirBuscaPaciente"
            @limpar-paciente="limparPaciente"
          />
        </div>
        
        <div class="modal-footer elegant-footer">
          <button type="button" class="btn btn-light" data-bs-dismiss="modal">
            <i class="fas fa-times me-2"></i>
            Cancelar
          </button>
          <button type="button" class="btn btn-success me-2" @click="saveAndGenerateFatura" :disabled="saving">
            <span v-if="saving" class="spinner-border spinner-border-sm me-2" role="status"></span>
            <i v-else class="fas fa-file-invoice me-2"></i>
            {{ saving ? 'Gerando...' : 'Salvar e Gerar Fatura' }}
          </button>
          <button type="button" class="btn btn-primary elegant-btn-primary" @click="save" :disabled="saving">
            <span v-if="saving" class="spinner-border spinner-border-sm me-2" role="status"></span>
            <i v-else class="fas fa-save me-2"></i>
            {{ saving ? 'Salvando...' : 'Salvar Orçamento' }}
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Modal de Busca de Paciente -->
  <paciente-busca-modal
    ref="pacienteBuscaModal"
    @paciente-selecionado="onPacienteSelecionado"
  />
</template>

<script>
import { orcamentoService } from '@/services/orcamentoService';
import { servicoProdutoService } from '@/services/servicoProdutoService';
import { openModal, closeModal } from '@/utils/modalHelper';
import { searchPacientes } from '@/services/pacientesService';
import { getDentistas } from '@/services/dentistasService';
import cSwal from '@/utils/cSwal.js';
import OrcamentoForm from './OrcamentoForm.vue';
import PacienteBuscaModal from '@/components/Global/PacienteBuscaModal.vue';

export default {
  name: 'OrcamentoModal',
  components: {
    OrcamentoForm,
    PacienteBuscaModal,
  },
  data() {
    return {
      saving: false,
      preselectedPaciente: null,
      pacienteSelecionado: null,
      pacientes: [],
      dentistas: [],
      servicosProdutos: [],
      orcamentoForm: {
        paciente_id: '',
        dentista_id: '',
        titulo: '',
        descricao: '',
        data_validade: '',
        observacoes: '',
        itens: [this.createEmptyItem()]
      },
      errors: {}
    };
  },
  methods: {
    open(pacienteId = null, pacienteObj = null) {
      this.preselectedPaciente = pacienteId;
      this.pacienteSelecionado = pacienteObj;
      this.resetForm();
      this.clearErrors();

      if (pacienteId) {
        this.orcamentoForm.paciente_id = pacienteId;
      }

      openModal('modalOrcamento');
    },

    resetForm() {
      this.orcamentoForm = {
        paciente_id: this.preselectedPaciente || '',
        dentista_id: '',
        titulo: '',
        descricao: '',
        data_validade: '',
        observacoes: '',
        itens: [this.createEmptyItem()]
      };
    },

    clearErrors() {
      this.errors = {};
    },

    updateOrcamentoForm(field, value) {
      this.orcamentoForm[field] = value;
    },

    createEmptyItem() {
      return {
        servico_produto_id: null,
        nome: '',
        descricao: '',
        quantidade: 1,
        valor_unitario: 0,
        desconto_percentual: 0,
        desconto_valor: 0,
        observacoes: ''
      };
    },

    addOrcamentoItem() {
      this.orcamentoForm.itens.push(this.createEmptyItem());
    },

    removeOrcamentoItem(index) {
      if (this.orcamentoForm.itens.length > 1) {
        this.orcamentoForm.itens.splice(index, 1);
      }
    },

    async searchServicos(termo) {
      try {
        const response = await servicoProdutoService.buscarParaOrcamento({ busca: termo });
        this.servicosProdutos = response.data.data || [];
      } catch (error) {
        console.error('Erro ao buscar serviços:', error);
      }
    },

    async save() {
      if (!this.validateForm()) {
        return;
      }

      this.saving = true;
      try {
        await this.saveOrcamento();
        this.closeModal();
        this.$emit('saved');
      } catch (error) {
        console.error('Erro ao salvar:', error);
        
        if (error.response && error.response.status === 422) {
          this.errors = error.response.data.data || {};
        } else {
          cSwal.cError('Erro ao salvar orçamento');
        }
      } finally {
        this.saving = false;
      }
    },

    async saveAndGenerateFatura() {
      if (!this.validateForm()) {
        return;
      }

      this.saving = true;
      try {
        const orcamento = await this.saveOrcamento();
        this.closeModal();
        
        // Emitir evento para abrir modal de fatura com dados do orçamento
        this.$emit('generate-fatura', orcamento);
      } catch (error) {
        console.error('Erro ao salvar:', error);
        
        if (error.response && error.response.status === 422) {
          this.errors = error.response.data.data || {};
        } else {
          cSwal.cError('Erro ao salvar orçamento');
        }
      } finally {
        this.saving = false;
      }
    },

    async saveOrcamento() {
      const response = await orcamentoService.createOrcamento(this.orcamentoForm);
      cSwal.cSuccess('Orçamento criado com sucesso');
      return response.data;
    },

    validateForm() {
      const validation = orcamentoService.validateOrcamentoData(this.orcamentoForm);
      this.errors = validation.errors;
      return validation.isValid;
    },

    closeModal() {
      closeModal('modalOrcamento');
    },

    abrirBuscaPaciente() {
      this.$refs.pacienteBuscaModal.open();
    },

    onPacienteSelecionado(paciente) {
      this.pacienteSelecionado = paciente;
      this.orcamentoForm.paciente_id = paciente.id;
    },

    limparPaciente() {
      this.pacienteSelecionado = null;
      this.orcamentoForm.paciente_id = '';
    },

    async loadPacientes() {
      try {
        const response = await searchPacientes();
        this.pacientes = response || [];
      } catch (error) {
        console.error('Erro ao carregar pacientes:', error);
        this.pacientes = [];
      }
    },

    async loadDentistas() {
      try {
        const response = await getDentistas();
        this.dentistas = response || [];
      } catch (error) {
        console.error('Erro ao carregar dentistas:', error);
        this.dentistas = [];
      }
    },

    async loadServicosProdutos() {
      try {
        const response = await servicoProdutoService.getServicosProdutos({ ativo: 1 });
        this.servicosProdutos = response.data.data || [];
      } catch (error) {
        console.error('Erro ao carregar serviços/produtos:', error);
        this.servicosProdutos = [];
      }
    }
  },

  mounted() {
    this.loadPacientes();
    this.loadDentistas();
    this.loadServicosProdutos();
  }
};
</script>

<style scoped>
/* Reutilizar estilos do FinanceiroCreateModal */
.elegant-modal {
  border-radius: 20px;
  border: none;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

.elegant-header {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  border: none;
  padding: 1.5rem 2rem;
}

.modal-icon {
  width: 50px;
  height: 50px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  font-size: 1.5rem;
}

.elegant-close {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  opacity: 1;
  color: white;
  font-size: 1.2rem;
  padding: 0.5rem;
  transition: all 0.3s ease;
}

.elegant-close:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.elegant-body {
  padding: 2rem;
  background: #f8f9fa;
}

.elegant-footer {
  background: white;
  border: none;
  padding: 1.5rem 2rem;
  gap: 1rem;
}

.elegant-btn-primary {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  border: none;
  border-radius: 10px;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  transition: all 0.3s ease;
}

.elegant-btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
}
</style>
